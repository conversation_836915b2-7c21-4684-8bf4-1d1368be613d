import Vue from 'vue';
import VueRouter from 'vue-router';

/* Layout */
import Layout from '../layout/index.vue';

Vue.use(VueRouter);

const routes = [
  {
    path: '/login', // 登录
    name: '登录',
    component: () => import('@/views/login/index')
  },
	/*{
    path: '/systemManagement',
    name: '系统管理',
    component: () => import('@/views/systemManagement/index')
  },*/
  {
    path: '/',
    component: Layout,
    name:'layout',
    // redirect: '/home',
    redirect: '/theme',
    children: [
      {
        path: '/setsystem', // 设置
        name: 'setsystem',
        component: () => import('@/views/setsystem/index')
    	},
      {
        path: '/themeInner',
        name: 'themeInner',
        component: () => import('@/views/themeInner/index')
      },
      {
        path: '/writeArticle',
        name: 'writeArticle',
        component: () => import('@/views/writeArticle/index')
	    },
	    {
        path: '/myDraft',
        name: 'myDraft',
        component: () => import('@/views/myDraft/index')
	    },
	    {
        path: '/searchResultPage',
        name: 'searchResultPage',
        component: () => import('@/views/searchResultPage/index')
	    },
      {
        path: '/createTheme', // 创建专题
        name: 'createTheme',
        component: () => import('@/views/createTheme/index')
	    },
	    {
	      path: '/topic', // 创建话题
	      name: 'topic',
	      component: () => import('@/views/topic/index')
	  	},
      {
        path: '/perCenter',
        name: 'perCenter',
        component: () => import('@/views/perCenter/index.vue')
    	},
      {
        path: '/home',
        // component:() => import('@/views/home/<USER>') ,
        component: () => import('@/views/theme/index') ,
        name:'home',
      },
      {
        path: '/theme',
        component:() => import('@/views/theme/index') ,
        name:'theme',
      },
      {
        path: '/follow',
        component:() => import('@/views/follow/index') ,
        name:'follow',
      },
      {
        path: '/articleDetail',
        component:() => import('@/views/articleDetail/index.vue') ,
        name:'articleDetail',
      },
      {
        path: '/myNotice',
        component:() => import('@/views/myNotice/index.vue') ,
        name:'myNotice',
      },

    ]
  },
  {
    path: '/personHome',
    component:() => import('@/views/home/<USER>') ,
    name:'personHome',
  },
  // ====================== 以下是新增的路由 ==========
  {
    path: '/myinfo',
    component:() => import('@/views/setsystem/myinfo.vue') ,
    name:'nyinfo',
  },
  {
    path: '/tipsManage',
    component:() => import('@/views/mySelf/tipsManage.vue') ,
    name:'tipsManage',
  },
  {
    path: '/topicManage',
    component:() => import('@/views/mySelf/topic.vue') ,
    name:'topicManage',
  },
  {
    path: '/sentiveWordManage',
    component:() => import('@/views/mySelf/sentiveWord.vue') ,
    name:'sentiveWordManage',
  },
  {
    path: '/announcementManage',
    component:() => import('@/views/mySelf/announcement.vue') ,
    name:'announcementManage',
  },
  {
    path: '/articleManage',
    component:() => import('@/views/mySelf/article.vue') ,
    name:'articleManage',
  },
  {
    path: '/artTipsCollectManage',
    component:() => import('@/views/mySelf/artTipsCollect.vue') ,
    name:'artTipsCollectManage',
  },
  {
    path: '/artTipsRecommendManage',
    component:() => import('@/views/mySelf/artTipsRecommend.vue') ,
    name:'artTipsRecommendManage',
  },
  {
    path: '/noticeManage',
    component:() => import('@/views/mySelf/notice.vue') ,
    name:'noticeManage',
  },
  {
    path: '/roleManage',
    component:() => import('@/views/mySelf/role.vue') ,
    name:'roleManage',
  },
  {
    path: '/topicRecommendManage',
    component:() => import('@/views/mySelf/topicRecommend.vue') ,
    name:'topicRecommendManage',
  },
];
routes.push({path: '/*', name: 'itsm404', component: () => import('@/views/error/itsm404')})
const router = new VueRouter({
    routes,
//  mode: 'hash',
//  base: 'lezhi',
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            if (from.meta.keepAlive) {
                from.meta.savedPosition = document.body.scrollTop;
            }
            return {x: 0, y: to.meta.savedPosition || 0};
        }
    }
});

export default router;
