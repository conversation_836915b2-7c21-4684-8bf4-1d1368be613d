@import './mixin.scss';
@import './common.scss';

#app {
  height: 100%;
  box-sizing: border-box;
  text-align: center;
  background: #fdfdfd;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
img[src=""],img:not([src]){

          opacity:0;

     }
// 字体引入
@font-face {
  font-family: PangMenZhengDao;
  src: url(./theme/fonts/PangMenZhengDao.ttf) format("truetype");
  font-weight: 700;
  font-display: "auto";
  font-style: normal
}

@font-face {
  font-family: HuXiaoBoKuHei;
  src: url(./theme/fonts/zhankukuhei.ttf) format("truetype");
  font-weight: 700;
  font-display: "auto";
  font-style: normal
}

.com-nav {
  background: #fff;
  width: 100%;
  height: 60px;
  box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  z-index: 1000;

  .nav {
    @include flex;
    width: 100%;
    height: 60px;
    margin: 0 auto;

    .logo {
      width: 110px;
      height: 100%;
      @include flex;
      @include flex-space-around;
      @include flex-align-center;
      margin-right: 35px;

      img {
        height: 30px;
      }

//    span{
//      color:#3E8CFF;
//      position:relative;
//      margin-left: 10px;
//      margin-bottom: -3px;
//      letter-spacing: 1px;
//    }
//    span:before{
//      content:'';
//      width:1px;
//      height:20px;
//      background: #D8D8D8;
//      left:-8px;
//      position:absolute;
//    }

    }

    .hidden_box {
      width: 162px;
      height: 132px;
      background: #FFFFFF;
      box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.1);
      border-radius: 0px 0px 2px 2px;
      position: absolute;
      top: 60px;
      left: calc(50% + 450px);
      padding: 0 17px;
      z-index: 99;

      p {
        width: 100%;
        padding: 16px 0;
        color: #303133;
        font-size: 16px;
        text-align: left;
      }

      ul {
        width: 100%;

        li {
          width: 100%;
          padding-top: 12px;
          text-align: left;
          @include flex;
          @include flex-align-center;

          img {
            margin-right: 5px;
          }
        }
      }
    }

    .nav_list {
      width: 1000px;
      height: 100%;
      margin: 0 auto;
      @include flex;
      @include flex-space-between;
      @include flex-align-center;
      position: relative;

      .left_nav {
        width: 282px;
        height: 100%;
        @include flex;
        @include flex-space-between;
        @include flex-align-center;

      }

      .nav_infor {
        @include flex;
        @include flex-align-center;
        // width: 270px;
        height: 100%;

        &:hover {
          cursor: pointer;
        }

        // position: absolute;
        // right: 0;
        // top: 0;
        .input:hover {
          cursor: pointer;
        }

        .popbox .el-popper{
          padding: 10px 0px;
        }

        .input,
        .infor,
        .message {
          height: 36px;
          color: #909199;
          @include flex;
          @include flex-align-center;
        }

        .input,
        .infor {
          width: 120px;
          font-size: 14px;

          img {
            margin-right: 5px;
            width: 20px;
          }
        }

        .infor,
        .message,
        .avatar {
          margin-left: 36px;
        }

        .avatar {
          width: 36px;
          height: 36px;
          box-sizing: border-box;
          margin-left: 40px;

          &:hover {
            cursor: pointer;
          }

          img {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border:1px solid #dfdfdf;
          }
        }

        .login_box {
          position: absolute;
          top: 0;
          right: -40px;
          width: 77px;
          height: 100%;
          color: #909199;
          background-color: #fff;
          font-size: 14px;
          @include flex;
          justify-content: space-between;
          align-items: center;

          &:hover {
            cursor: pointer;
          }
        }
      }

      .el-input__suffix {
        display: none;
      }

      .el-input.el-input--suffix,
      .el-select {
        width: 450px;
      }

      .el-input__inner {
        border: none;
      }

      .search_box {
        height: 100%;
        width: 600px;
        position: absolute;
        top: 0;
        left: 145px;
        background-color: #fff;
        @include flex;
        @include flex-align-center;

        i {
          font-size: 16px;
          color: #C0C4CC;
        }

        .el-input{
          font-size:13px;
        }

      }
    }

  }
}

.home_content {
  width: 990px;
  margin: 0px auto;
  @include flex;
  @include flex-space-between;

  .left_home_content {
    width: 604px;

    .home_title {
      width: 100%;
      height: 32px;
      @include flex;
      justify-content: flex-start;

      &>div {
        height: 30px;
        line-height: 30px;
        margin-right: 20px;
        font-size: 13px;
        text-align: left;
        color: #909199;
        cursor:pointer;

        i {
          margin-right: 1px;
          display:inline-block;
          vertical-align: middle;
        }

        &:first-of-type {
          color: #3E8CFF
        }
      }
    }

    .type-list{
      margin-bottom: 5px;
      ul{
        text-align: left;
        li:first-of-type{
          padding-left:2px;
        }
        li{
          display: inline-block;
          font-size:13px;
          color: #909199;
        }
        .active{
          font-weight: bold;
          color: #303133;
          font-size:15px;
          animation: fade-in .3s;
        }
        li:hover{
          opacity: 1;
        }
      }
    }

    .frame {
      width: 604px;
//    height: 125px;
      background: #fff;
      border: 1px solid rgba($color: #C0C4CC, $alpha: 0.8);
      border-radius: 3px;
      position: relative;
      text-align: left;
      padding: 0 10px;
      margin-bottom: 15px;

      .image {
        width: 100%;
        height: 30px;
        line-height: 30px;

        img {
          height: 12px;
          margin-right: 10px;
        }
      }

      .edit-area {
        width: 100%;
        height: 70px;
        margin: 10px 0;
        resize: none;
        overflow-y: auto;
        overflow-wrap: break-word;
        line-height: 18px;
        font-size: 13px;
        border: 0 !important;
        outline: none;
        color: #606166;
      }

      .edit-area:empty:before{
        content:'分享点滴知识…';
        color: #C0C4CC;
      }
    }
    .frame_image {
//    height: 218px;
      padding: 0 10px;
//    border: 1px solid #3E8CFF;
//
//    .triangle {
//      border-color: #3E8CFF;
//    }

      .limit_num {
        width: 100%;
        height: 25px;
        color: #C0C4CC;
        font-size: 12px;
      }

      .uoload {
        width: 100%;
        height: 65px;
        font-size:0px;

        i.el-icon-plus {
          font-size: 14px;
        }

        .el-upload-list__item.is-ready {
          width: 62px;
          height: 62px;
          line-height: 62px;

          img {
            height: 62px;
          }
        }

        .picture-card{
          width: 62px;
          height: 62px;
          line-height: 62px;
          background: #f4f4f4;
          display: inline-block;
          text-align: center;
          position: relative;
          .el-image{
            vertical-align: top;
            img{
              vertical-align: middle;
              max-width: 62px;
              max-height: 62px;
            }
            .el-image__inner--center{
              display:inline-block;
              top:auto;
              left:auto;
              transform: none;
            }
          }
        }

        .el-upload--picture-card {
          width: 62px;
          height: 62px;
          line-height: 62px;
          display:inline-block;
        }
      }
    }

    .frame_video {
//    height: 205px;
//    border: 1px solid #3E8CFF;
//
//    .triangle {
//      border-color: #3E8CFF;
//    }

      .video_upload {
        width: 68px;
        height: 68px;
        line-height: 68px;
        background-color: #f4f4f4;
        border-radius: 6px;
        margin-bottom: 10px;
        text-align: center;
      }
    }

    .frame_theme {
      position: relative;
      z-index:999;

      .theme_list {
        margin-bottom: 100px;
        position: absolute;
        top: 0px;
        left: 80px;
        width: 300px;
        background: #FFFFFF;
        box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
        border-radius: 2px;
        overflow-y: auto;

        .tip {
          width: 100%;
          height: 35px;
          font-size: 13px;
          color: #909199;
          padding: 0 20px;

          span {
            color: #3E8CFF
          }
        }

        ul {
          width: 100%;
          padding: 20px 0 0 0;

          li {
            height: 35px;
            line-height: 35px;
            list-style: none;
            color: #606166;
            font-size: 13px;
            @include flex;
            @include flex-space-between;
            cursor: pointer;
            padding: 0px 20px;

            &>span:last-of-type {
              color: #909199;
              font-size: 12px;
            }
          }
          li:hover{
             background:#eee;
             opacity: 0.6;
          }
        }
      }
    }

    .triangle {
      width: 10px;
      height: 10px;
      display: inline-block;
      position: absolute;
      top: -5px;
      left: 30px;
      background: #fff;
      border-bottom: 1px solid rgba($color: #C0C4CC, $alpha: 0.6);
      border-right: 1px solid rgba($color: #C0C4CC, $alpha: 0.6);
      transform: rotate(225deg);
      -ms-transform: rotate(225deg);
      /* IE 9 */
      -moz-transform: rotate(225deg);
      /* Firefox */
      -webkit-transform: rotate(225deg);
      /* Safari 和 Chrome */
      -o-transform: rotate(225deg);
      /* Opera */
    }

    // .textarea {
    //   width: 100%;
    //   height: 110px;
    //   background: #fff;
    //   border: 1px solid #C0C4CC;
    //   border-radius: 3px;
    // }

    .empty {
      width: 100%;
      font-size: 13px;
      color: #C0C4CC;
      margin-top: 80px;
    }
  }

  .right_home_content {
    width: 370px;
    margin-top: 32px;

    &>div {
      margin-bottom: 24px;
    }

    .user_infor {
      width: 100%;
      height: 130px;
      padding: 12px;
      border-radius: 3px;
      background: #fff;
      border: 1px solid rgba($color: #C0C4CC, $alpha: 0.8);

      .infor_top {
        @include flex;

        .image {
          img{
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #dfdfdf;
          }
        }

        .user {
          height: 40px;
          width: calc(100% - 40px);
          margin-left: 10px;
          line-height: 20px;

          .name {
            text-align: left;
            font-size: 13px;
            color: #303133;
            padding-bottom: 2px;
          }

          .sign {
            text-align: left;
            font-size: 12px;
            color: #909199;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .infor_bottom {
        margin-top: 20px;
        width: 100%;
        height: 58px;
        @include flex;

        .tip_content {
          width: 25%;
          height: 100%;
          text-align: center;
          cursor: pointer;

          .num {
            font-size: 16px;
            color: #303133;
            font-weight: bold;
          }

          .tip {
            width: 100%;
            height: 20px;
            font-size: 12px;
            color: #909199;
            margin-top: 5px;
            @include flex;
            @include flex-align-center;
            @include flex-vertical-center;

            img {
              width: 12px;
              height: 12px;
              margin-right: 5px;
            }
          }
        }
      }
    }

    .create_center {
      width: 100%;
      border: 1px solid rgba($color: #C0C4CC, $alpha: 0.8);
      border-radius: 3px;
      background: #fff;

      .center_title {
        @include flex;
        @include flex-align-center;
        width: 100%;
        height: 40px;
        padding: 12px;
        text-align: left;

        span {
          font-size: 13px;
          font-weight: bold;
          color: #303133;
          margin-left: 2px;
        }

        img {
          width: 14px;
          margin-right: 5px;
        }
      }

      .center_num {
        @include flex;
        border-bottom: 1px solid #F6F6F6;

        &>div {
          width: 50%;
          height: 95px;
          line-height: 25px;
          padding-bottom: 25px;
          font-size: 12px;
          color: #909199;

          .num {
            color: #303133;
            font-size: 16px;
            font-weight: bold;
          }

          span{
            margin-left: 5px;
            font-size: 13px;
          }
          .add{
            color: #1FC618;
          }
          .reduce{
            color: #FF7676;
          }

          &:first-of-type {
            border-right: 1px solid #F6F6F6;
          }
        }
      }

      .center_tip {
        @include flex;

        &>div {
          @include flex;
          @include flex-vertical-center;
          @include flex-align-center;
          width: 33%;
          height: 60px;
          text-align: left;

          img {
            width: 25px;
            margin-right: 5px;
          }

          &:first-of-type>img {
            width: 32px;
          }

          p:first-of-type {
            font-size: 14px;
            color: #303133;
            font-weight: bold;
          }

          p:last-of-type {
            font-size: 12px;
            color: #909199;
          }
        }
      }
    }

    .hot_topic {
      width: 100%;
      height: 380px;
      // padding:16px 0;
      border: 1px solid rgba($color: #C0C4CC, $alpha: 0.8);
      border-radius: 3px;
      background: #fff;

      .topic_title {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 12px 10px 12px 12px;
        ;
        @include flex;
        @include flex-space-between;
        @include flex-align-center;

        &>div:first-of-type {
          color: #303133;
          font-size: 13px;
          font-weight: bold;
        }

        &>div:last-of-type {
          color: #C0C4CC;
          font-size: 13px;
          margin-right: 5px;

          img {
            height: 12px;
          }
        }
      }

      .content {
        width: 100%;
        padding: 0 16px;

        ul {
          width: 100%;

          li {
            font-size: 13px;
            color: #00C7FF;
            @include flex;
            @include flex-space-between;
            height: 32px;
            line-height: 32px;

            &>span:last-of-type {
              font-size: 13px;
              color: #C0C4CC;
            }

            &>span:first-of-type {
              color: #303133;
              display: inline-block;
              width: calc(100% - 50px);
              text-align: left;
              display:inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}

.article_content {
  width: 100%;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #C0C4CC;
  padding: 12px 10px;
  margin-bottom: 10px;

  .top_content {
    @include flex;
    @include flex-space-between;

    .user_infor {
      @include flex;
      justify-content: flex-start;

      .image {

        overflow: hidden;
        img{
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border:1px solid #dfdfdf;
        }
      }

      .user {
        height: 50px;
        margin-left: 10px;
        line-height: 25px;

        .name {
          text-align: left;
          font-size: 13px;
          color: #303133;
        }

        .sign {
          font-size: 12px;
          color: #909199;

          &>span {
            padding: 10px 3px 10px 0;
          }
        }
      }
    }

    .follow {
      width: 55px;
      height: 24px;
      background: #186EF7 linear-gradient(270deg, #186EF7 0%, #36A8FD 48%, #186EF7 100%);
      border-radius: 18px;
      opacity: 0.92;
      font-size: 12px;
      line-height: 24px;
      color: #fff;
      cursor:pointer;
    }

    .already_follow {
      background: #fff;
      color: #3E8CFF;
      border: 1px solid #3E8CFF;
    }
  }

  .middle_content {
    text-align: left;
    width: 100%;
    margin-bottom: 30px;

    .article_title {
      height: 40px;
      line-height: 40px;
      font-size: 13px;
      font-weight: bold;
      color: #303133;
    }

    .article {
      text-align: left;
      font-size: 13px;
      color: #606166;
//    display: -webkit-box;
//    -webkit-box-orient: vertical;
//    -webkit-line-clamp: 3;
//    overflow: hidden;
      img{
          max-width:100%;
      }
      table {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
        word-wrap:break-word;
      }
      table td,
      table th {
        border-bottom: 1px solid #ccc;
        border-right: 1px solid #ccc;
        padding: 0px 5px;
        line-height:19px;
      }
      table th {
        border-bottom: 1px solid #ccc;
        background: #f1f1f1;
        text-align: center;
        line-height:18px;
      }

      /* blockquote 样式 */
      blockquote {
        display: block;
        border-left: 8px solid #d0e5f2;
        padding: 5px 10px;
        margin: 10px 0;
        line-height: 1.4;
        font-size: 100%;
        background-color: #f1f1f1;
      }

      /* code 样式 */
      code {
        display: inline-block;
        *display: inline;
        *zoom: 1;
        background-color: #f1f1f1;
        border-radius: 3px;
        padding: 3px 5px;
        margin: 0 3px;
      }
      pre code {
        display: block;
      }

      /* ul ol 样式 */
      ul, ol {
        margin: 10px 0 10px 20px;
      }

    }

  }

  .bottom_content {
    @include flex;
    justify-content: flex-start;
    position: relative;

    &>div {
      margin-right: 28px;
      cursor:pointer;
      @include flex;
      @include flex-align-center;

      img {
        width: 15px;
        margin-right: 5px;
      }

      span {
        font-size: 12px;
        color: #909199;
      }
    }
    .train-request{
      position:absolute;
      right:-20px;
      top:0px;
      display:none;
    }
  }
  .knowledge_content {
    text-align: left;
    width: 100%;
    margin-bottom: 20px;

    .content {
      font-size: 13px;
      color: #303133;
      font-weight: normal;
      .topic .topic-mark {
        color: #00C7FF;
      }
    }

    .picture {
      margin-top: 20px;
      display: inline-block;
      >div{
        display: inline-block;
        margin-right: 8px;
        margin-bottom:8px;
      }
    }

    .video {
      margin-top: 20px;
      border:1px solid white;
    }
  }
}
.article_content:hover {
  .bottom_content .train-request{
    display: flex;
    animation: show-fade-in 2s;
  }
}



.article_5 {
  height: 200px;

  .middle_content {
    margin-bottom: 38px;

    .article_title {
      font-weight: normal;
    }

    .link {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      background: #F6F6F6;
      border-radius: 1px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      padding: 0 10px;

      span {
        margin-left: 5px;
        color: #606166;
      }
    }
  }
}

.login_home {
  background: url('../assets/image/pwbg.png') no-repeat;
  background-size: cover;
  width: 1440px;
  height: 900px;
  min-height: 900px;
  overflow: hidden;
  margin: 0 auto;

  .login_box {
    width: 456px;
    height: 570px;
    background: #FFFFFF;
    box-shadow: 0px 0px 25px -5px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    margin: 244px auto;

    .register {
      width: 100%;
      font-size: 12px;
      font-weight: 400;
      color: #1968FA;
      padding: 21px;
      text-align: right;

      &:hover {
        cursor: pointer;
      }
    }

    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }

    .el-tabs__nav.is-top {
      top: 0;
      left: -116px;
      margin-left: 50%;

    }

    .el-tabs__item {
      font-size: 24px;
    }


    .message_form {
      position: relative;

      .valid_code {
        width: 93px;
        height: 40px;
        position: absolute;
        top: 0px;
        left: 320px;

        &>.el-form-item__content {
          line-height: 28px;
        }

        &>.el-form-item {
          margin-bottom: 7px;
          height: 28px;
          font-size: 14px;

          .el-button {
            width: 100%;
            padding: 7px;
            border-radius: 1px;
          }
        }

      }
    }
  }

  .form_content {
    width: 100%;
    padding: 0 39px;
    margin-top: 105px;
    text-align: left;

    .el-form {
      width: 378px;
      border: none;

      input:-internal-autofill-selected {
        background-color: #fff !important;
      }

      .el-input__inner {
        border: none;
        border-bottom: 1px solid #e8e8e8;
        border-radius: 0;
      }

      .el-button {
        width: 100%;
      }

      .forget_password {
        font-size: 12px;
        @include flex;
        @include flex-space-between;
        margin: 0 0px 90px 10px;

        &>span {
          color: #1968FA;
        }
      }

      .mg-password {
        margin: 0 0px 90px 9px;
      }
    }
  }

  .register_box {
    width: 456px;
    height: 570px;
    background: #FFFFFF;
    box-shadow: 0px 0px 25px -5px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    margin: 244px auto;

    .login {
      width: 100%;
      font-size: 12px;
      font-weight: 400;
      color: #1968FA;
      padding: 21px;
      text-align: right;

      &:hover {
        cursor: pointer;
      }
    }

    &>.title {
      width: 100%;
      height: 24px;
      font-size: 24px;
      font-weight: 400;
      color: #3E8CFF;
      margin: 30px auto;
      margin-bottom: 80px;
    }
  }
}

.setUser {
  width: 1000px;
  margin: 0 auto;
  position: relative;

  .userInfor {
    width: 560px;
    margin-left: 36px;

    .user_title {
      text-align: left;
      width: 100%;
      padding: 16px 0 35px 0;
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }

    .avatar_box {
      width: 100%;
      height: 108px;
      @include flex;
      flex-direction: column;
      justify-content: flex-start;
      margin-bottom: 15px;

      .avatar-uploader {
        width: 62px;
        height: 36px;
      }

      .image {
        width: 62px;
        height: 62px;
        line-height: 62px;
        border: 1px dotted #d9d9d9;
        border-radius: 50%;
        overflow: hidden;

        i {
          color: #8c939d;
        }

        img {
          width: 62px;
          height:62px;
        }
      }

      .change_avatar {
        width: 72px;
        height: 36px;
        line-height: 36px;
        font-weight: 400;
        color: #C0C4CC;

        &:hover {
          cursor: pointer;
        }
      }
    }

    .el-form {
      text-align: left;

      .el-form-item {
        .el-input{
          width:202px;
        }
        .el-input__inner {
          width: 202px;
          height: 34px;
          background: #FCFCFC;
          border-radius: 2px;
          border: 1px solid #C0C4CC;
          font-size:13px;
        }



        .el-textarea__inner {
          width: 474px;
          height: 79px;
          background: #FCFCFC;
          border-radius: 2px;
          border: 1px solid #C0C4CC;
          font-size:13px;
        }

        .el-form-item__content {
          text-align: left;
        }

        .el-radio {
          cursor: pointer;
          margin-right: 30px;
          .el-radio__label{
            font-size: 13px;
            color: #606166;
          }
          .el-radio__inner{
            width:13px;
            height:13px;
          }
        }

        .el-form-item__label {
          font-size: 13px;
          color: #909199;
        }
      }

      .change_psw,
      .change_num {
        .el-form-item__content {
          @include flex;
          justify-content: space-between;

          span {
            display: inline-block;
            width: 560px;
            height: 100%;
            font-size: 13px;
            color: #3E8CFF;
          }
        }

      }

      .submit_button {
        border: none;
        width: 80px;
        height: 36px;
        background: #3E8CFF;
        box-shadow: 0px 2px 6px 0px #438BF6;
        border-radius: 18px;
        opacity: 0.92;
        color: #FFFFFF;
        margin-top: 20px;
        font-size:13px;
      }
    }
  }

  .nav {
    height: 60px;
    width: 810px;
    text-align: left;
    position: fixed;
    top: 0px;
    left: 50%;
    margin-left: -355px;
    @include flex;
    @include flex-align-center;
    background: #FFF;
    font-size: 14px;
    font-weight: bold;
    color: #3E8CFF;
    z-index:1001;
  }
}

.theme_home {
  position: relative;

  &>.el-button {
    padding: 9px 12px;
    border-radius: 2px;
    border: 1px solid #3E8CFF;
    font-size: 13px;
    color: #3E8CFF;
    position: absolute;
    top: 28px;
    right: calc(50% - 498px);

    &:hover {
      background: #3E8CFF;
      color: #fff;
    }
  }

  >span{
    padding: 9px 0px;
    font-size: 13px;
    color: #c0c4cc;
    position: absolute;
    top: 28px;
    left:calc(50% - 498px);
    .active{
      color: #3E8CFF;
      opacity: 0.8;
    }
    span{
      cursor:pointer;
    }
  }

  .theme_title {
    width: 100%;
    height: 240px;
    line-height: 90px;
    background: url('../assets/image/tbg.png') no-repeat;
    background-size: cover;

    &>div {
      width: 445px;
      height: 90px;
      font-size: 34px;
      color: #3E8CFF;
      margin: 0 auto;
      @include flex;
      @include flex-align-center;

      &>.title {
        margin: 0 2rem;
        font-family: HuXiaoBoKuHei;
      }

      &>.tip1 {
        font-size: 16px;
      }
    }
  }

  .referesh{
    width:100%;
    float:left;
    margin-top: 5px;
    color:#C0C4CC;
    font-size:13px;
    img{
      margin-bottom: -3px;
    }
  }

  .theme_content {
    width: 1026px;
    overflow: hidden;
    margin: -170px auto;

    .theme_box {
      float: left;
      margin: 12px;
      width: 232px;
      height: 250px;
      background: #FFFFFF;
      box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      position:relative;

      .image {
        width: 100%;
        padding: 18px 80px 8px 80px;

        &>div {
          width: 62px;
          height: 62px;
          border-radius: 50%;
          border: 1px solid #dfdfdf;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .title {
        width: 100%;
        height: 18px;
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-top:5px;
      }

      .des {
        font-size: 12px;
        font-weight: 400;
        color: #606166;
        line-height: 20px;
        text-align: center;
        margin: 10px 30px 12px 30px;
        min-height:40px;
      }

      .num {
        width: 100%;
        font-size: 12px;
        font-weight: 400;
        color: #909199;
        padding: 0 30px 12px 30px;

        span {
          margin: 0 5px;
        }
      }

      .el-button {
        padding: 8px 18px;
        background: #FFFFFF;
        border-radius: 2px;
        border: 1px solid #3E8CFF;
        font-size: 13px;
        font-weight: 400;
        color: #3E8CFF;

        &:hover {
          background: #3E8CFF;
          color: #fff;
        }
      }

      .admin{
        position:absolute;
        content:'';
        background: #FFBC41;
        right:10px;
        top:10px;
        padding:3px 10px;
        color:white;
      }
    }
  }
}

.themeInner_content {
  padding: 33px 0;
  width: 1000px;
  margin: 0 auto;
  @include flex;
  justify-content: flex-start;
  position: relative;

  .left_content {
    width: 660px;

    .theme_infor {
      width: 100%;
      height: 62px;
      @include flex;
      @include flex-align-center;
      @include flex-space-between;

      .theme {
        height: 100%;
        @include flex;
        @include flex-align-center;

        .image {
          width: 62px;
          height: 62px;
          border-radius: 50%;
          border:1px solid #dfdfdf;
          overflow: hidden;

          img {
            width: 62px;
          }
        }

        .infor {
          text-align: left;

          .title {
            margin-top:10px;
            font-size: 18px;
            font-weight: bold;
            color: #303133;
            padding: 0px 14px;

          }

          .detail {
            font-size: 13px;
            font-weight: 400;
            color: #909199;
            padding: 10px 14px;
          }
        }
      }

      .el-button {
        padding: 6px 16px 6px 14px;
        background: #3E8CFF;
        box-shadow: 0px 2px 6px 0px #438BF6;
        border-radius: 22px;
        opacity: 0.92;
        font-size: 13px;
        font-weight: 400;
        color: #FFFFFF;
        border: none;

        &>span {
          display: block;
          width: 100%;
          @include flex;
          @include flex-align-center;
          @include flex-vertical-center;
        }

        img {
          width: 20px;
          height: 22px;
          margin-right: 5px;
        }
      }
    }

    .articel_title {
      margin-top: 40px;
      position:relative;

      .classify-select{
        font-size:13px;
        color:#606166;
        font-weight: bold;
        cursor:pointer;
        z-index:99;
        outline: none;
        height: 40px;
        display: inline-block;
        border-bottom: 1px solid #e3e3e3;
        line-height:40px;
        padding-right: 30px;
      }

      .el-tabs{
        display: table-cell;
        width:100%;
      }

      .el-tabs__nav{
//      margin-left: 150px;
      }

      .el-tabs__active-bar {
        background: #606166;
      }

      .el-tabs__item.is-active {
        color: #606166;
      }

      .el-tabs__item:hover {
        color: #606166;
      }

      .el-tabs__item {
        color: #909199;
      }

      .el-tabs__nav-wrap::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background-color: #E3E3E3;
        z-index: 1
      }
    }
  }

  .right_content {
    width: 300px;
    text-align: left;
    margin-left: 40px;

    .theme_des {
      margin-bottom: 24px;
      .content {
        font-size: 13px;
        font-weight: 400;
        color: #606166;
        padding-bottom:15px;
        border-bottom: 2px solid #f4f4f4;
        line-height: 28px;
      }
    }

    .creator {
      border-bottom: 2px solid #f4f4f4;
      margin-bottom: 24px;

      .infor {
        @include flex;
        @include flex-align-center;
        font-size: 13px;
        color: #303133;
        margin-bottom: 24px;

        img {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          border: 1px solid #dfdfdf;
          margin-right: 8px;
        }
      }
    }

    .follow {
      .follow_theme {
        @include flex;
        @include flex-align-center;
        font-size: 13px;
        font-weight: bold;
        color: #3E8CFF;

        &:hover {
          cursor: pointer;
        }

      }
      img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        margin-right: -6px;
        border: 1px solid #dfdfdf;
      }


    }

    .subtheme-manage{
      border-top: 2px solid #f4f4f4;
      margin-top: 24px;
      .operate{
        padding:20px 0;
        font-weight: bold;
        color: #3E8CFF;
        font-size: 13px;
        cursor: pointer;
       }
    }

    .title {
      font-size: 13px;
      font-weight: 400;
      color: #909199;
      margin-top:10px;
      padding-bottom: 10px;

    }
  }

  .nav {
    @include flex;
    @include flex-space-between;
    @include flex-align-center;
    height: 60px;
    width: 855px;
    text-align: left;
    position: fixed;
    top: 0px;
    left: 50%;
    margin-left: -355px;
    background-color: #fff;
    z-index:1001;

    .left_nav {
      line-height: 20px;
      font-size: 13px;
      color: #909199;
      padding-left: 22px;
      border-left: 2px solid #eaeaea;
      height: 40px;

      .title {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
      }
    }

    .right_nav {
      width:220px;
      height: 100%;
      @include flex;
      @include flex-align-center;
      justify-content: flex-end;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #909199;

      i {
        font-size: 18px;
        margin-right: 7px;
      }
    }
  }
}

.center_home {
  width: 1000px;
  margin: 0 auto;
  text-align: left;
  box-sizing: border-box;
  padding-top: 37px;

  .user_infor {
    width: 100%;
    @include flex;
    @include flex-align-center;
    margin-bottom: 10px;

    .left_infor {
      width: 62px;
      height: 62px;

      img {
        width: 62px;
        height: 62px;
        border-radius: 50%;
        border: 1px solid #dfdfdf;
      }
    }

    .right_infor {
      .name {
        padding: 0 0 0 13px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .sign {
        padding: 6px 0 0 13px;
        font-size: 13px;
        color: #606166;
      }
    }
  }

  .user_follow {
    height: 100%;

    .el-tabs--left .el-tabs__active-bar.is-left {
      left: auto;
      right: 174px;
      height: 15px !important;
      top: 13px;
    }

    .el-tabs__nav-wrap {
      position: static;
    }

    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }

    .el-tabs--left .el-tabs__item.is-left {
      text-align: left;
      font-size:13px;
    }

    .tab_name {
      width: 143px;
      height: 45px;
      @include flex;
      @include flex-space-between;
    }

    .el-tabs--left .el-tabs__header.is-left {
      position: relative;
      top: 38px;
      left: 10px;
      border-top: 2px solid #f3f3f3;
      padding-top: 20px;
//    border-right: 2px solid #f3f3f3;
    }

    .top_nav {
      .el-tabs__item.is-active {
        color: #606166;
      }

      .el-tabs__content {
        border-left: 2px solid #f3f3f3;
        margin-top: -15px;
        min-height:380px;
      }

      .el-tabs__item:hover {
        color: #606166;
        cursor: pointer;
      }

      .el-tabs__item {
        color: #909199;
        font-size:13px;
      }

      .el-tabs__active-bar {
        background-color: #606166;
      }

      .el-tabs__nav-wrap::after {
        background-color: #f3f3f3;
      }

      .el-tabs__item.is-top:nth-child(2) {
        padding-left: 30px;
      }

      .list_content {
        .infor_content {
          width: 100%;
          height: 64px;
          border-bottom: 2px solid #f8f8f8;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left_content {
            display: flex;
            padding-left: 30px;

            .title{
              color: #303133;
              line-height: 40px;
            }
            .classify{
              line-height: 40px;
              margin-left: -15px;
            }
            .image {
              width: 40px;
              height: 40px;
              margin-right: 10px;

              img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border:1px solid #dfdfdf;
              }
            }

            .follow_infor {
              text-align: left;
              font-size: 12px;
              color: #C0C4CC;

              div {
                &>span:first-of-type {
                  margin-left: 0;
                }

                span {
                  margin: 5px;

                }
              }

              &>div:first-of-type {
                font-size: 13px;
                color: #303133;
                margin-bottom: 2px;
              }
            }
          }

          .right_content {
            .manage-button{
              color:#3E8CFF;
              cursor: pointer;
              margin-right: 60px;
              font-size: 13px;
            }
            .el-button {
              width: 82px;
              height: 28px;
              line-height: 20px;
              border-radius: 18px;
              opacity: 0.92;
              border: 1px solid #C0C4CC;
              font-size: 13px;
              // color: #C0C4CC;
              padding: 0;
              position:relative;

              i {
                margin-right: 2px;
              }
            }
            .el-button:focus {
              color:#606266;
              background: transparent;
            }
            .el-button:hover {
              border: 1px solid #3E8CFF;
              color:#3E8CFF;
            }
            .follow_button:hover:before{
              display:inline-block;
            }
            .follow_button:before{
              display:none;
              content:'取消关注';
              position:absolute;
              left:12px;
              top:3px;
              width: 60px;
              height: 20px;
              line-height: 20px;
              background: white;
              color:#3E8CFF;
            }
            .recommend_button:hover:before{
              display:inline-block;
            }
            .recommend_button:before{
              display:none;
              content:'取消推荐';
              position:absolute;
              left:12px;
              top:3px;
              width: 60px;
              height: 20px;
              line-height: 20px;
              background: white;
              color:#3E8CFF;
            }
          }
        }
        .nav-content{
          position: absolute;
          top: -40px;
          left: 15px;
          color: #606166;
          font-size:13px;
          width:100%;
          .classify-select{
            cursor:pointer;
            z-index:99;
            outline: none;
            height: 40px;
            display: inline-block;
            line-height:40px;
            padding-right: 30px;
          }
          .el-cascader{
            .el-input{
              width:400px;
              .el-input__inner{
                background-color:transparent;
                color: #C0C4CC;
                border:none;
                font-size:13px;
              }
              .el-input__suffix{
                display: none;
              }
            }
          }
        }
      }
    }
    .no_nav{
      margin-top: 38px;
      border-top: 2px solid #f3f3f3;
      border-left: 2px solid #f3f3f3;
      min-height: 380px;
    }
  }
}

.articleDetail_content {
  width: 1000px;
  margin: 0 auto;
  @include flex;
  @include flex-space-between;
  position: relative;
//transform: translate(0, 0);

  .detail_left {
    width: 686px;
    margin-bottom: 60px;

    .article_container {
      padding: 35px 23px 35px 18px;
      width: 100%;
      text-align: left;
      background: #FFFFFF;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);

      .article_title {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 34px;

      }

      .article_author {
        width: 100%;
        height: 40px;
        margin-left: 5px;
        @include flex;
        @include flex-align-center;

        .author_infor {
          @include flex;

          .image {
            width: 40px;
            height: 40px;
            margin-right: 12px;

            img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              border: 1px solid #dfdfdf;
            }
          }

          .infor {
            line-height: 20px;

            .name {
              font-size: 13px;
              color: #303133;

            }

            .time {
              font-size: 12px;
              color: #C0C4CC;

              &>span {
                margin-right: 18px;

                span {
                  margin-right: 5px;
                }
              }
            }
          }
        }

        .follow {
          margin-left: auto;

          .el-button {
            background: #186EF7 linear-gradient(270deg, #186EF7 0%, #36A8FD 48%, #186EF7 100%);
            border-radius: 18px;
            opacity: 0.92;
            color: #fff;
            padding: 7px 17px;
            border: none;
            font-size:13px;
            width:75px;
          }
        }
        .already-follow {
          margin-left: auto;

          .el-button {
            border-radius: 18px;
            opacity: 0.92;
            padding: 7px 17px;
            background: #fff;
            color: #3E8CFF;
            border: 1px solid #3E8CFF;
            font-size:13px;
            width:75px;
          }
        }
      }

      .article {
        width: 100%;
        padding: 30px 0 15px 0;
//      @include flex;
        flex-direction: column;
        word-break: break-all;
        span{
          white-space: normal;
        }
        *{
          max-width: 100%;
        }
        pre{
          white-space: pre-wrap;           /* css-3 */
          white-space: -moz-pre-wrap;      /* Mozilla, since 1999 */
          white-space: -pre-wrap;          /* Opera 4-6 */
          white-space: -o-pre-wrap;        /* Opera 7 */
          word-wrap: break-word;           /* Internet Explorer 5.5+ */
        }

        i.icon-xiegang {
          font-size: 10px;
          color: #3E8CFF;

          &:last-of-type {
            margin-right: 8px;
          }
        }
        h1,h2,h3{
          padding-top: 80px;
          margin: -70px 0 10px 0;
          line-height: 1.5;
        }
        h1{font-size:18px;}
        h2{font-size:17px;}
        h3{font-size:16px}
        h4{font-size:15px}
        h5{font-size:14px}
        h6{font-size:13px}

        &>div {
          &>p {
            margin-bottom: 1rem;
          }

          &>p:last-of-type {
            margin-bottom: 0;
          }
        }

        p {
          font-size: 13px;
          color: #606166;
          line-height: 27px;
        }

        img {
          min-width: 200px;
          max-width: 100%;
          margin: 15px;
        }
        table {
          border-top: 1px solid #ccc;
          border-left: 1px solid #ccc;
          word-wrap:break-word;
        }
        table td,
        table th {
          border-bottom: 1px solid #ccc;
          border-right: 1px solid #ccc;
          padding: 3px 5px;
        }
        table th {
          border-bottom: 2px solid #ccc;
          background: #f1f1f1;
          text-align: center;
        }

        /* blockquote 样式 */
        blockquote {
          display: block;
          border-left: 8px solid #d0e5f2;
          padding: 5px 10px;
          margin: 10px 0;
          line-height: 1.4;
          font-size: 100%;
          background-color: #f1f1f1;
        }

        /* code 样式 */
        code {
          display: inline-block;
          *display: inline;
          *zoom: 1;
          background-color: #f1f1f1;
          border-radius: 3px;
          padding: 3px 5px;
          margin: 0 3px;
        }
        pre code {
          display: block;
        }

        /* ul ol 样式 */
        ul, ol {
          margin: 10px 0 10px 20px;
        }
      }

      &>.infor {
        margin-top: 20px;
        width: 100%;
        @include flex;
        position:relative;

        span {
          display: inline-block;
          @include flex;
          @include flex-align-center;
          margin-right: 28px;
          font-size: 13px;
          color: #C0C4CC;

          img {
            width: 15px;
            height: 15px;
            margin-right: 5px;
          }
        }
        .train-request{
          position:absolute;
          right:20px;
          top:0px;
          display:none;
          font-size:13px;
          color:#C0C4CC;
          img{
            margin-right: 5px;
          }
        }
      }
    }

    .article_container:hover {
      &>.infor .train-request{
        display: flex;
        animation: show-fade-in 2s;
      }
    }

    .author_detail {
      background-color: #fff;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
      width: 100%;
      height: 114px;
      @include flex;
      margin-top: 5px;
      padding: 20px 18px;

      .author_avatar {
        width: 75px;
        height: 75px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 1px solid #dfdfdf;
        }
      }

      .author_right {
        margin-left: 18px;
        text-align: left;
        width: 555px;

        .infor_top {
          width: 100%;
          height: 50%;
          @include flex;
          @include flex-space-between;
          @include flex-align-center;
          line-height: 20px;

          .name {
            font-size: 13px;
            color: #303133;
          }

          .company {
            font-size: 12px;
            color: #C0C4CC;
          }

          .top_right {
            .el-button {
              padding: 7px 17px;
              background: #186EF7 linear-gradient(270deg, #186EF7 0%, #36A8FD 48%, #186EF7 100%);
              border-radius: 18px;
              opacity: 0.92;
              color: #fff;
              font-size: 13px;
              width:75px;
            }
          }

          .top_right1 {
            .el-button {
              padding: 7px 17px;
              background: #fff;
              color: #3E8CFF;
              border: 1px solid #3E8CFF;
              border-radius: 18px;
              opacity: 0.92;
              font-size: 13px;
              width:75px;
            }
          }
        }

        .infor_bottom {
          line-height: 50px;
          font-size: 13px;
          color: #909199;

        }
      }
    }


  }

  .detail_right {
    width: 300px;
    height:100%;

    .image {
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .05);
      margin-bottom: 5px;
      width: 100%;
      height: 70px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .recommend_read {
      width: 100%;
      text-align: left;
      padding: 15px 12px;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .05);
      margin-bottom: 5px;

      .title {
        width: 100%;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;

        i {
          color: #3E8CFF;
          margin-right: 5px;
        }
      }

      .title_list {
        width: 100%;
        margin-bottom: 8px;


        &:last-of-type {
          margin-bottom: 0;
        }

        .title_detail {
          font-size: 13px;
          color: #303133;
          line-height: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }

        .num {
          font-size: 12px;
          color: #909199;

          span {
            line-height: 20px;
            margin-left: 5px;
          }
        }
      }
    }

    .picture {
      width: 100%;
      height: 128px;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .05);
      margin-bottom: 5px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .nav {
      width: 100%;
      text-align: left;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .05);
      padding: 15px 12px;

      .title {
        width: 100%;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;

        i {
          color: #3E8CFF;
          margin-right: 5px;
        }
      }

      .title_nav {
        &>div {
          line-height: 30px;
          font-size: 12px;
          color: #606166;

          i {
            font-size: 12px;
            margin-right: 10px;
            color: #fff;
          }
        }
        a{
            color:#333;
        }
      }
    }
  }

  .fixed_content {
    position: fixed;
    top: 220px;
    right: calc(25% - 250px);

    &>div,.el-popover__reference{
      &>div {
        border-radius: 50%;
        width: 40px;
        height: 40px;
        line-height: 40px;
        background: #FFFFFF;
        box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.1);

        i {
          font-size: 15px;
          color: #C0C4CC;
        }
      }

      span {
        display: inline-block;
        font-size: 12px;
        font-weight: 400;
        color: #C0C4CC;
        margin: 8px 0 8px 0;
      }
    }
  }
}

.createTheme {
  .submit_button {
    padding: 10px 23px;
    width: 110px !important;
    height: 36px !important;
  }

  .image {
    width: 71px;
    height: 71px;
    border-radius: 0 !important;
    border: none !important;
  }

  .cover-image{
    width: 62px;
    height: 62px;
    line-height: 62px;
    border: 1px dotted #d9d9d9;
    border-radius: 50%;
    overflow: hidden;
    box-sizing: border-box;
    img{
        width:62px;
        height:62px;
    }
  }

  .el-form .el-form-item{
    .el-input,.el-input__inner {
      width: 350px !important;
    }
  }

  .nav {
    height: 60px;
    width: 810px;
    text-align: left;
    position: fixed;
    top: 0px;
    left: 50%;
    margin-left: -355px;
    @include flex;
    @include flex-align-center;
    background: #FFF;
    font-size: 14px;
    font-weight: bold;
    color: #3E8CFF;
    z-index:1001;
  }
}

.topic_content {

  width: 1000px;
  margin: 0 auto;
  @include flex;
  @include flex-space-between;
  position: relative;

  .left_topic {
    width: 680px;
    // padding: 25px 0;

    .topic_title {
      @include flex;
      @include flex-space-between;
      @include flex-align-center;
      padding: 0 15px;

      .user_infor {
        @include flex;
        justify-content: flex-start;
        @include flex-align-center;

        .image {
          width: 90px;
          height: 90px;
          border-radius: 50%;
          position: relative;
          margin-top:10px;
          border:1px solid #dfdfdf;

          .content {
            width: 30px;
            height: 30px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-left: -15px;
            margin-top: -10px;
            font-size: 40px;
            color: #fff;
            opacity: 0.9;
          }

          img {
            width: 90px;
            height: 90px;
          }
        }

        .user {
          height: 60px;
          margin-left: 0px;
          line-height: 30px;

          .name {
            margin-top: 10px;
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            padding-left:3px;
            width: 400px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .sign {
            font-size: 13px;
            color: #909199;
            text-align: left;
          }
        }
      }

      .follow {
        padding: 8px 25px;
        background: #3E8CFF;
        box-shadow: 0px 2px 6px 0px #438BF6;
        border-radius: 18px;
        opacity: 0.92;
        font-size: 13px;
        color: #FFFFFF;
      }
      .already_follow {
        background: #fff;
        color: #3E8CFF;
        border: 1px solid #3E8CFF;
        box-shadow:none;
      }
    }

    .publish_topic {
      width: 679px;
      background: #FFFFFF;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.09);
      margin: 20px 0 20px 0;
      padding: 15px 0;
      >img{
        width:42px;
        height:42px;
        vertical-align: top;
        display: inline-block;
        margin-right:5px;
        margin-top:10px;
        border: 1px solid #dfdfdf;
        border-radius: 50%;
      }
      >div{
        display: inline-block;
        width: calc(100% - 70px);
        .frame {
//        height: 125px;
          background: #fff;
          border: 1px solid rgba($color: #C0C4CC, $alpha: 0.8);
          border-radius: 3px;
          position: relative;
          text-align: left;
          padding: 0 10px;
          margin-bottom: 15px;

          .image {
            width: 100%;
            height: 30px;
            line-height: 30px;

            img {
              height: 12px;
              margin-right: 10px;
            }
          }

          .edit-area {
            width: 100%;
            height: 70px;
            margin: 10px 0;
            resize: none;
            overflow-y: auto;
            overflow-wrap: break-word;
            line-height: 18px;
            font-size: 13px;
            border: 0 !important;
            outline: none;
            color: #606166;
          }

          .edit-area:empty:before{
            content:'写下你的想法…';
            color: #C0C4CC;
          }
        }
        .frame_image {
//        height: 218px;
          padding: 0 10px;
    //    border: 1px solid #3E8CFF;
    //
    //    .triangle {
    //      border-color: #3E8CFF;
    //    }

          .limit_num {
            width: 100%;
            height: 25px;
            color: #C0C4CC;
            font-size: 12px;
          }

          .uoload {
            width: 100%;
            height: 65px;
            font-size:0px;

            i.el-icon-plus {
              font-size: 14px;
            }

            .el-upload-list__item.is-ready {
              width: 62px;
              height: 62px;
              line-height: 62px;

              img {
                height: 62px;
              }
            }

            .picture-card{
              width: 62px;
              height: 62px;
              line-height: 62px;
              background: #f4f4f4;
              display: inline-block;
              text-align: center;
              position: relative;
              .el-image{
                vertical-align: top;
                img{
                  vertical-align: middle;
                  max-width: 62px;
                  max-height: 62px;
                }
                .el-image__inner--center{
                  display:inline-block;
                  top:auto;
                  left:auto;
                  transform: none;
                }
              }
            }

            .el-upload--picture-card {
              width: 62px;
              height: 62px;
              line-height: 62px;
              display:inline-block;
            }
          }
        }

        .frame_video {
//        height: 205px;
    //    border: 1px solid #3E8CFF;
    //
    //    .triangle {
    //      border-color: #3E8CFF;
    //    }

          .video_upload {
            width: 68px;
            height: 68px;
            line-height: 68px;
            background-color: #f4f4f4;
            border-radius: 6px;
            margin-bottom: 10px;
            text-align: center;
          }
        }

        .frame_theme {
          position: relative;
          z-index:999;

          .theme_list {
            margin-bottom: 100px;
            position: absolute;
            top: 0px;
            left: 80px;
            width: 300px;
            background: #FFFFFF;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
            border-radius: 2px;
            overflow-y: auto;

            .tip {
              width: 100%;
              height: 35px;
              font-size: 13px;
              color: #909199;
              padding: 0 20px;

              span {
                color: #3E8CFF
              }
            }

            ul {
              width: 100%;
              padding: 20px 0px;

              li {
                height: 35px;
                line-height: 35px;
                list-style: none;
                color: #606166;
                font-size: 13px;
                @include flex;
                @include flex-space-between;
                cursor: pointer;
                padding: 0px 20px;

                &>span:last-of-type {
                  color: #909199;
                  font-size: 12px;
                }
              }
              li:hover{
                 background:#eee;
                 opacity: 0.6;
              }
            }
          }
        }

        .empty {
          width: 100%;
          font-size: 13px;
          color: #C0C4CC;
          margin-top: 200px;
        }
      }
    }
  }

  .right_topic {
    padding: 33px 0;
    width: 300px;
    text-align: left;

    .title {
      width: 100%;
      font-size: 13px;
      color: #909199;
      margin-bottom: 15px;
    }

    .topic_introduction {
      width: 100%;
      padding: 0 0 35px 0;
      border-bottom: 2px solid #f4f4f4;
      margin-bottom: 24px;

      .sign {
        width: 100%;
        font-size: 13px;
        color: #606166;
      }
    }

    .creator {
      padding-bottom: 22px;
      border-bottom: 2px solid #f4f4f4;
      margin-bottom: 24px;

      .image {
        height: 35px;
        @include flex;
        @include flex-align-center;

        img {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          margin-right: 8px;
          border:1px solid #dfdfdf;
        }

        span {
          font-size: 13px;
          color: #303133;

        }
      }

    }

    .attend_person {
      width: 100%;
      padding-bottom: 24px;
      border-bottom: 2px solid #f4f4f4;

      .image {
        width: 100%;
        height: 35px;

        img {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          margin-right: -6px;
          border:1px solid #dfdfdf;
        }
      }
    }
  }

  .nav {
    @include flex;
    @include flex-align-center;
    height: 60px;
    width: 855px;
    text-align: left;
    position: fixed;
    top: 0px;
    left: 50%;
    margin-left: -355px;
    background-color: #fff;
    z-index:1001;

    .left_nav {
      line-height: 20px;
      font-size: 13px;
      color: #909199;
      padding-left: 22px;
      border-left: 2px solid #eaeaea;
      height: 40px;

      .title {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
      }
    }
  }




  .articel_title {
    .el-tabs__active-bar {
      background: #606166;
    }

    .el-tabs__item.is-active {
      color: #606166;
    }

    .el-tabs__item:hover {
      color: #606166;
    }

    .el-tabs__item {
      color: #909199;
    }
  }

}

.editor {
  position: relative;

  .editor_title {
    width: 100%;
    height: 70px;
    white-space: nowrap;
    text-align: left;

    .el-input {
      font-size: 14px;
      font-weight: bold;
      color: #909199;
      border: none;
      height: 70px;
      width:750px;

      .el-input__inner {
        border: none;
        height: 70px;
        line-height: 70px;
        background-color: #fdfdfd;
        padding:0px 40px 0px 0px;
      }
    }
  }

  .container {
    text-align: left;
    height: 422px;

    .ql-container {
      height: 380px;
    }

  }

  .footer {
    line-height: 40px;
    box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.1);
    padding: 0 17px;
    width: 1000px;
    height: 40px;
    background: #F8F8F8;
    @include flex;
    @include flex-space-between;
    font-size: 13px;
    color: #606166;

    .left_footer {
      span {
        margin-right: 8px;
      }
    }

    .right_footer {
      span {
        margin: 0 10px;
      }

      .el-button {
        padding: 8px 10px;
        color: #fff;
        background: #3E8CFF;
        border-radius: 2px;
        font-size:13px;
      }
    }
  }

  .el-input__suffix:hover {
    cursor: pointer;
  }
}

// 屏幕自适应
@media screen and (max-width: 1600px) {
  .container {
    height: 1080px;
  }
}

.el-loading-spinner{
  .el-loading-text{
    font-size:13px;
    color:#C0C4CC;
  }
  .circular{
    height:32px;
    width:32px;
  }
  .path{
    stroke:#C0C4CC;
  }
}
.el-image-viewer__canvas{
    text-align:center;
    height:100vh;
    line-height:100vh;
}
.el-image-viewer__img{
    vertical-align: middle;
}
.el-select-dropdown__empty{
  font-size: 13px;
}
.el-select-dropdown__item{
  font-size: 13px;
}
.article-contain{
  h1, h2, h3, h4, h5, h6 { font-size: 100%;font-weight: normal }
}
.comment_content {
  width: 100%;
  background-color: #fff;
  margin-top: 24px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .05);

  .comment {
    width: 100%;
    padding: 20px;
    @include flex;

    >img {
      width: 35px;
      height: 35px;
      border-radius: 50%;
    }

    .edit-area {
      width: 440px;
      background: #FCFCFC;
      border-radius: 2px;
      margin: 0 0 0 20px;
      font-size:13px;
      border:1px solid #C0C4CC;
      line-height: 20px;
      text-align: left;
      padding: 6px 10px 0 10px;
      outline:none;
    }

    .edit-area:empty:before{
      content:'写下你的评论…';
      color: #C0C4CC;
    }

    .el-button {
      width: 60px;
      height: 34px;
      padding: 0;
      border-radius: 2px;
      font-size:13px;
    }
  }

  .sub-comment {
    width: 90%;
    margin-top:20px;
    @include flex;

    .reply-obj{
      white-space: nowrap;
      padding: 6px 0px;
      font-size: 13px;
      color:#909199;
    }

    .edit-area {
      width: 70%;
      background: #FCFCFC;
      border-radius: 2px;
      font-size:13px;
      border:1px solid #C0C4CC;
      line-height: 20px;
      text-align: left;
      padding: 6px 10px 0 10px;
      outline:none;
    }

    .edit-area:empty:before{
      content:'写下你的评论…';
      color: #C0C4CC;
    }

    .el-button {
      width: 60px;
      height:34px;
      padding: 0;
      border-radius: 2px;
      font-size:13px;
    }
  }

  .comment_all {
    width: 100%;
    font-size: 14px;
    font-weight: bold;
    color: #303133;
    text-align: left;
    padding: 20px 15px 10px;

    i {
      color: #3E8CFF;
    }
  }

  .comment_box {
    width: 100%;
    padding: 0 44px 0 19px;

    // .el-pager {
    //   li {
    //     border-radius: 50%;
    //     border: 1px solid #C0C4CC;
    //     padding: 0;
    //     width: 25px;
    //     height: 25px;
    //     line-height: 25px;
    //     margin:0 8px;
    //   }
    // }

    .comment_parent,
    .comment_children>div {
      width: 100%;
      @include flex;
      padding: 18px 0;
      border-bottom: 2px solid #f4f4f4;

      >img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
//      border: 1px solid #333;
      }

      .right_comment {
        width: 100%;

        .name {
          @include flex;
          @include flex-space-between;
          margin-top: 6px;
          margin-left: 8px;

          .user {
            font-size: 13px;
            font-weight: 400;
            color: #303133;
            .role {
              padding: 0px 8px !important;
              margin-left: 5px;
              border-radius: 2px !important;
              border: 1px solid #3E8CFF !important;
              font-size: 10px !important;
              color: #3E8CFF !important;
              height:20px;
            }
          }

          span {
            font-size: 13px;
            color: #C0C4CC;
            display: inline-block;
          }
        }

        &>.comment_container {
          font-size: 13px;
          color: #606166;
          text-align: left;
          line-height: 24px;
          padding: 0 8px;
          margin-top:8px;
        }

        &>#comment_num {
          text-align: left;
          margin: 8px 0 0 8px;
          color:#C0C4CC;


          i {
            font-size: 16px;
          }
        }
        .hide_area{
          display: none;
        }
      }
      .right_comment:hover {
        .hide_area{
          display: inline;
        }
      }
    }

    .comment_children {
      flex-direction: column;
      padding: 0 0 0 35px;

      &>div {
        .role {
          padding: 0px 8px !important;
          margin-left: 5px;
          border-radius: 2px !important;
          border: 1px solid #3E8CFF !important;
          font-size: 10px !important;
          color: #3E8CFF !important;
          height:20px;
        }
      }
    }
  }
}
.el-message__content{
    font-size: 13px;
}
.article_infor {
  width: 100%;
  .article {
    width: 100%;
    padding: 22px 0;
    text-align: left;
    border-bottom: 2px solid #f4f4f4;
    word-break: break-all;

    .title {
      font-size: 13px;
      font-weight: bold;
      color: #303133;
      padding-bottom: 12px;

      &:hover {
        cursor: pointer;
      }
    }

    .content {
      font-size: 13px;
      font-family: SourceHanSansCN-Normal, SourceHanSansCN;
      font-weight: 400;
      color: #606166;
      line-height: 19px;
      margin-bottom: 10px;
      img{
        max-width: 100%;
      }
      table {
        border-top: 1px solid #ccc;
        border-left: 1px solid #ccc;
        word-wrap:break-word;
      }
      table td,
      table th {
        border-bottom: 1px solid #ccc;
        border-right: 1px solid #ccc;
        padding: 0px 5px;
        line-height:18px;
      }
      table th {
        border-bottom: 1px solid #ccc;
        background: #f1f1f1;
        text-align: center;
        line-height:19px;
      }

      /* blockquote 样式 */
      blockquote {
        display: block;
        border-left: 8px solid #d0e5f2;
        padding: 5px 10px;
        margin: 10px 0;
        line-height: 1.4;
        font-size: 100%;
        background-color: #f1f1f1;
      }

      /* code 样式 */
      code {
        display: inline-block;
        *display: inline;
        *zoom: 1;
        background-color: #f1f1f1;
        border-radius: 3px;
        padding: 3px 5px;
        margin: 0 3px;
      }
      pre code {
        display: block;
      }

      /* ul ol 样式 */
      ul, ol {
        margin: 10px 0 10px 20px;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .footer {
      width: 100%;
      @include flex;
      margin-top: 12px;
      font-size: 12px;
      color: #909199;
      position:relative;

      .user {
        @include flex;
        @include flex-align-center;

        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          margin-right: 5px;
          border:1px solid #dfdfdf;
        }
      }

      .infor {
        span {
          display: inline-block;
          @include flex;
          @include flex-align-center;
          margin-right: 28px;

          img {
            width: 15px;
            height: 15px;
            margin-right: 5px;
          }
        }
      }

      &>div {
        @include flex;
        @include flex-space-between;
        margin-right: 40px;
      }

      .train-request{
        position:absolute;
        right:10px;
        top:0px;
        display:none;
        img{
          margin-right: 5px;
        }
      }
    }
  }
  .article:hover {
    .footer .train-request{
      display: flex;
      animation: show-fade-in 2s;
    }
  }
}
.el-message-box__message{
    font-size:13px;
}
.overflow-deal{
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.el-loading-mask{
  z-index:888;
  background: transparent;
}
.emoji-wrap{
  z-index:999 !important;
}
.att-block{
  border: 1px solid #ddd;
  padding: 2px 5px;
  color: #909199;
  display: inline-block;
  margin-right:8px;
  margin-top:5px;
  a{
    color: #909199;
  }
}
.submit-button{
  width: 64px;
  height: 32px;
  background: #3e8cff;
  border-radius: 2px;
  color: #FFFFFF;
  font-size:12px;
  border: none;
  outline: none;
}
.article-video-play{
  color: white;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  content: '';
  background:url(../assets/image/audio-play.png);
  width:30px;
  height:30px;
  background-size: cover;
  display:block;
}
.theme-classify{
  height: 30px;
  border-radius: 2px;
  font-weight: bold;
  display: inline-block;
  padding: 0px 15px;
  line-height: 30px;
  cursor:pointer;
  margin-right:20px;
  margin-bottom:10px;
}
.theme-classify-0{
   background: #E6F0FF;
   color: #408CFE;
}
.theme-classify-1{
   background: #FFF2DA;
   color: #FFBC41;
}
.theme-classify-2{
   background: #F0E9FF;
   color: #6528F4;
}
.theme-classify-3{
   background: #D6F3FF;
   color: #25B3EA;
}
.theme-classify-4{
   background: #DFFFF6;
   color: #00C994;
}
.theme-classify-create{
   background: #F2F2F2;
   color: #909199;
}
.el-message-box{
  .el-message-box__title{
    font-size:16px;
  }
  .el-message-box__content{
    font-size:13px;
    .el-input{
      font-size:13px;
      height:36px;
      line-height:36px;
    }
  }
}
.common-dialog{
  .el-dialog__header{
    text-align: left;
    border-bottom: 1px solid #dfdfdf;
    .el-dialog__title{
      font-size: 15px;
    }
  }
  .el-dialog__body{
    font-size: 13px;
  }
}
.el-autocomplete-suggestion{
  li{
    font-size:13px;
  }
}
.el-autocomplete{
  .el-input{
    font-size:13px;
  }
}
.el-dropdown-menu__item{
  font-size:13px;
}
.article-keywords{
  text-align: left;
  .keyword,.new-keyword{
    padding:2px 5px;
    border:1px solid #ddd;
    border-radius:2px;
    color:#909199;
    display:inline-block;
    margin-top: 5px;
  }
  .keyword{
    margin-right: 10px;
  }
}
.notice-area{
  font-size:13px;
  color:#606166;
  .title{
    margin:30px 0 20px -5px;
    font-size:14px;
    color:#606166;
    font-weight: bold;
    i {
      color: #3e8cff;
      margin-right: 5px;
      font-weight: bold;
    }
  }
  .el-form-item__label{
    font-size:13px;
    color:#606166;
    text-align: left;
  }
  .el-form-item__content{
    font-size:13px;
    color:#606166;
  }
  .el-form-item{
    margin-bottom:8px;
  }
  .el-textarea,.el-select__input{
    font-size:13px;
  }
  .el-select{
    width:100%;
  }
}
//-------------版主指定树  start -------------
.tree-dialog{
  text-align: left;
  .el-dialog{
    .el-dialog__header{
      padding: 15px 20px;
      color: #303133;
      border-bottom: 1px solid #F4F3F4;
    }
    .el-dialog__body{
      padding: 28px 20px;
    }
  }
}

.person-tree-def{
    background: transparent;
    .el-checkbox__inner{
        background: transparent;
    }
    >.el-tree-node{
        position:relative;
        >.el-tree-node__content{
            height:36px;
            background: #F5F6F6;
            position: sticky;
            top:0;
            z-index:2;
            width:100%;
            border-bottom: 1px solid #E9E9E9;
        }
    }
}

.search-tree-def{
    background: transparent;
    .el-checkbox__inner{
        background: transparent;
    }
}

.tree-scrollbar{
    .el-scrollbar__wrap{
        overflow-x:hidden;
    }
    .is-horizontal {
        display: none;
    }
}

.search-input-def{
    width:calc(100% - 40px);
    margin:20px;
    line-height:32px;
    .el-input__inner{
        height:32px;
        line-height:32px;
        border: 1px solid #C0C4CC;
        border-radius: 16px;
        font-size:12px;
        background:transparent;
    }
    .el-input__suffix{
        right:10px;
    }
}
//-------------版主指定树  end -------------
.el-switch{
  .el-switch__label span{
    font-size:13px;
  }
  .el-switch__label--right{
    margin-left:5px;
  }
  .el-switch__label--left{
    margin-right:5px;
  }
  .el-switch__core{
    width:35px !important;
    height:16px;
  }
  .el-switch__core:after{
    width:12px;
    height:12px;
  }
}
.classify-item{
  font-size:13px;
  max-height:230px;
  overflow-y: auto;
  li{
    padding:10px;
    cursor:pointer;
  }
  li:hover{
    background: #f8f8f8;
    opacity: 0.6;
  }
}
.classify-position{
  display: table-cell;
  white-space: nowrap;
  vertical-align: top;
}
.el-cascader-panel{
  font-size:13px;
}
.notice-box{
  height:300px;
  font-size:13px;
  cursor:default;
  .header{
    height:40px;
    line-height: 40px;
    border-bottom: 1px solid #ddd;
    .classify{
      width:30%;
      text-align: center;
      display:inline-block;
      color:#909199;
      vertical-align: middle;
      i{
        position:relative;
        .num{
          position: absolute;
          border-radius: 50%;
          width: 18px;
          height: 18px;
          top: -10px;
          right: -10px;
          background: #ff6262;
          font-size: 10px;
          line-height: 18px;
          color: white;
        }
      }
    }
    .line{
      color:#ddd;
    }
    .active{
      color:#3E8CFF;
    }
  }
  .content{
    height:220px;
    .empty{
      color:#C0C4CC;
      font-size: 13px;
      height: 100%;
      text-align: center;
      padding:70px;
    }
    .content-item{
      font-size:13px;
      line-height: 30px;
      padding: 6px 20px;
      text-align: left;
    }
    .content-item:not(:last-child){
      border-bottom: 1px solid #eee;
    }
  }
  .footer{
    height:40px;
    line-height:40px;
    border-top: 1px solid #ddd;
  }
}
.notice-icon{
  position:relative;
  .num{
    position: absolute;
    border-radius: 50%;
    width: 4px;
    height: 4px;
    top: -8px;
    right: 2px;
    background: #ff6262;
  }
}
.href-addr{
  color:#3E8CFF;
  cursor:pointer;
}
@keyframes show-fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
.noticeUserPopper{
  .el-select-dropdown__item{
    height:50px;
    line-height:50px;
    .avatar{
      margin-right: 10px;
      vertical-align: middle;
      border-radius: 50%;
      border: 1px solid #dfdfdf;
      width:40px;
      height:40px;
    }
  }
}
