<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <h2>小知识管理</h2>
        <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
      </div>
      <el-row :gutter="20">
        <el-col :span="10" :offset="1">
          <el-row>
            <el-button type="success" @click="handleOpenAddDialog">新增</el-button>
          </el-row>
        </el-col>
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="60">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容"
              width="300">
              <template slot-scope="scope">
                <div class="article" v-dompurify-html="scope.row.content"/>
              </template>
            </el-table-column>
            <!--<el-table-column
              prop="author"
              label="作者"
              width="120">
            </el-table-column>-->
            <el-table-column
              prop="status"
              label="状态"
              width="120">
            </el-table-column>
            <el-table-column
              prop="commentCount"
              label="评论数"
              width="120">
            </el-table-column>
            <el-table-column
              prop="recommendCount"
              label="推荐数"
              width="120">
            </el-table-column>
            <el-table-column
              prop="collectionCount"
              label="收藏数"
              width="120">
            </el-table-column>
            <!--<el-table-column
              prop="type"
              label="类型"
              width="120">
            </el-table-column>-->
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="120">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="160">
              <template slot-scope="scope">
                <!--<el-button @click="handleGetArtTipDetail(scope.row)" type="text" size="small">查看</el-button>-->
                <!--<el-button @click='gotoTipsRecommend(scope.row)' type="text" size="small">推荐管理</el-button>-->
                <!--<el-button @click='gotoTipsCollect(scope.row)' type="text" size="small">收藏管理</el-button>-->
                <el-button @click='handleUpdateTip(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleDeleteTip(scope.row)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增小知识" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="关联话题" :label-width="formLabelWidth" :style="{width: '40%'}">
            <el-select v-model="form.topicId" placeholder="请选择话题" >
              <el-option v-for="item in topics()" :key="item.id" :label="item.topic" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogFormVisible = false,handleAddTip()">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑小知识" :visible.sync="editDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="状态" :label-width="formLabelWidth" :style="{width: '40%'}">
            <el-select v-model="form.status" placeholder="请选择状态" >
              <el-option label="删除" value="删除"></el-option>
              <el-option label="正常" value="正常"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="editDialogFormVisible = false,handleSubmitEdit()">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
import {tips, topics} from './data';

const api = new httpNew();
export default {
  data() {
    return {
      tableData: [],
      dialogFormVisible: false,
      editDialogFormVisible: false,
      form: {
        content: '',
        topicId: '',
        status: '',
      },
      formLabelWidth: '120px',
    }
  },
  mounted() {
    this.handleGetArtTipList()
  },
  methods: {
    topics() {
      return topics
    },
    handleGetArtTipList(){
      let params = {
        pageNum: 1,
        pageSize: 10,
        topicId: this.$route.query.topicId,
      }
      api.getArtTipList(params).then(res => {
        let tips = res.data.data.list
        if (tips && tips.length > 0) {
          let arr = tips.map(item => {
            return {
              ...item,
              status: item.status == '1' ? "正常" : "删除",
            }
          })
          this.tableData = arr;
        }
      })
      let topicObj = this.topics().find(item => item.id == this.$route.query.topicId);
      tips.forEach(item => {
        let obj = {...item};
        obj.content = '《' + topicObj.topic + '》' + item.content;
        this.tableData.push(obj);
      })
    },
    handleOpenAddDialog(){
      this.form = {
        content: '',
        topicId: '',
        status: '正常',
      }
      this.dialogFormVisible = true
    },
    /*gotoTipsRecommend(row){
      var routeUrl=this.$router.resolve({name:'artTipsRecommendManage',query: { topicId: this.$route.query.topicId, tipId: row.id }});
      window.open(routeUrl.href, '_blank');
    },
    gotoTipsCollect(row){
      var routeUrl=this.$router.resolve({name:'artTipsCollectManage',query: { topicId: this.$route.query.topicId, tipId: row.id }});
      window.open(routeUrl.href, '_blank');
    },*/
    handleAddTip() {
      api.addArtTip(this.form).then(res => {
        if (res.status == "0"){
          this.$message.success('新增成功')
          this.handleGetArtTipList()
        }else {
          this.$message.error('新增失败')
        }
      })
    },
    handleUpdateTip(row) {
      this.form = JSON.parse(JSON.stringify(row))
      let topicObj = this.topics().find(item => item.id == this.$route.query.topicId);
      this.form.content = this.form.content.replace('《' + topicObj.topic + '》', '');
      this.editDialogFormVisible = true
    },
    handleSubmitEdit() {
      api.updateArtTip(this.form).then(res => {
        if (res.data.status == "0"){
          this.$message.success('修改成功')
          this.handleGetArtTipList()
        }else {
          this.$message.error('修改失败')
        }
      })
    },
    handleDeleteTip(row) {
      api.deleteArtTip(row.id).then(res => {
        if (res.status == "0"){
          this.$message.success('删除成功')
          this.handleGetArtTipList()
        }else {
          this.$message.error('删除失败')
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
