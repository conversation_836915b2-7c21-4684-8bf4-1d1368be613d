<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>专题创建角色管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success" @click="handleOpenAddDialog">新增</el-button>
        </el-row>
      </el-col>
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="150">
          </el-table-column>
          <el-table-column
            prop="name"
            label="名称"
            width="300">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间"
            width="200">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作">
            <template slot-scope="scope">
              <!--<el-button @click="selectTopic(scope.row)" type="text" size="small">查看</el-button>-->
              <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
              <el-button @click='deleteRole(scope.row)' type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="新增角色" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="名称" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,addSubjectRole()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑角色" :visible.sync="editDialogFormVisible">
      <el-form :model="form">
        <el-form-item label="名称" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="editDialogFormVisible = false,updateRole()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';
import {roles} from './data';

const api = new httpNew();
export default {
  name: 'role',
  data() {
    return {
      tableData: [],
      dialogFormVisible: false,
      editDialogFormVisible: false,
      form: {
        id: '',
        content: '',
      },
      formLabelWidth: '120px',
    }
  },
  created() {
    this.tableData = roles;
  },
  methods: {
    handleOpenAddDialog(){
      this.form.id = '';
      this.form.content = '';
      this.dialogFormVisible = true;
    },
    handleOpenEditDialog(row){
      this.form.id = row.id;
      this.form.content = row.name;
      this.editDialogFormVisible = true;
    },
    addSubjectRole(){
      api.addRole(this.form).then(res => {
        if (res.data.status == "0") {
          this.$message({message: '新增角色成功', type:'success'});
        } else {
          this.$message({message: res.data.msg, type: 'error'});
        }
      })
    },
    updateRole() {
      api.editRole(this.form).then(res => {
        if (res.data.status == "0") {
          this.$message({message: '编辑角色成功', type:'success'});
        } else {
          this.$message({message: res.data.msg, type: 'error'});
        }
      })
    },
    deleteRole(row) {
      api.removeRole(row.id).then(res => {
        if (res.data.status == "0") {
          this.$message({message: '删除角色成功', type:'success'});
          this.tableData.splice(this.tableData.indexOf(row), 1);
        } else {
          this.$message({message: res.data.msg, type: 'error'});
        }
      })
    },
  }
}
</script>

<style scoped>

</style>
