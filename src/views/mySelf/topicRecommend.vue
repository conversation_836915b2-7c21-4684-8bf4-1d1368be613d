<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>话题推荐管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <!--<el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success">新增</el-button>
        </el-row>
      </el-col>-->
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="80">
          </el-table-column>
          <el-table-column
            prop="topic"
            label="话题">
          </el-table-column>
          <el-table-column
            prop="suggestion"
            label="推荐意见">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间"
            width="120">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <!--<el-button @click="handleGetTopicDetail(scope.row)" type="text" size="small">详情</el-button>-->
              <!--<el-button @click='handleAddTopicRecommend(scope.row)' type="text" size="small">新增推荐</el-button>-->
              <el-button @click='handleRemoveTopicRecommend(scope.row)' type="text" size="small">删除推荐</el-button>
              <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">更新推荐</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="编辑推荐" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="推荐意见" :label-width="formLabelWidth">
          <el-input v-model="form.suggestion" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,handleUpdateTopicRecommend()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';
import {topicRecommends} from './data';

const api = new httpNew();

export default {
  name: 'topicRecommend',
  data() {
    return {
      tableData: [],
      topicDetail: {},
      topicRecommend: {},
      dialogFormVisible: false,
      form: {
        suggestion: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetTopicSuggestionList();
  },
  methods: {
    async handleGetTopicSuggestionList() {
      try {
        let res = await api.getTopicSuggestionList(this.$route.query.topicId)
        this.tableData = res.data.data
      }catch (e) {}
      this.tableData = topicRecommends;
    },
    handleOpenEditDialog(row) {
      // this.topicRecommend = row;
      this.form.suggestion = row.suggestion;
      this.dialogFormVisible = true;
    },
    /*handleGetTopicDetail(row) {
      api.getTopicsDetail(row.id).then(res => {
        this.topicDetail = res.data;
      });
    },*/
    /*handleAddTopicRecommend(row) {
      api.addTopicRecommend(this.topicRecommend).then(res => {
        if (res.status == "0") {
          this.$message.error('新增推荐失败');
        }
        this.$message.success('新增推荐成功');
      });
    },*/
    handleRemoveTopicRecommend(row) {
      api.removeTopicRecommend(row.id).then(res => {
        if (res.status == "0") {
          this.$message.success('删除推荐成功');
          this.handleGetTopicSuggestionList();
        }else {
          this.$message.error('删除推荐失败');
        }
      });
    },
    handleUpdateTopicRecommend() {
      api.updateTopicRecommend(this.form).then(res => {
        if (res.status == "0") {
          this.$message.success('更新推荐成功');
          this.handleGetTopicSuggestionList();
        }else {
          this.$message.error('更新推荐失败');
        }
      });
    }
  }
};
</script>
<style>
</style>
