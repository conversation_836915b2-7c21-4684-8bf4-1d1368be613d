<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>话题管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <!--<el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success">新增</el-button>
        </el-row>
      </el-col>-->
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="80">
          </el-table-column>
          <el-table-column
            prop="topic"
            label="内容">
          </el-table-column>
          <!--<el-table-column
            prop="author"
            label="作者"
            width="120">
          </el-table-column>-->
          <el-table-column
            prop="status"
            label="状态"
            width="120">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间"
            width="120">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <el-button @click="gotoTipsManage(scope.row)" type="text" size="small">小知识</el-button>
              <el-button @click="gotoTopicRecommend(scope.row)" type="text" size="small">推荐管理</el-button>
              <el-button @click="selectTopic(scope.row)" type="text" size="small">详情</el-button>
              <el-button @click='updateTopic(scope.row)' type="text" size="small">编辑</el-button>
              <el-button @click='deleteTopic(scope.row)' type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="编辑话题" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="内容" :label-width="formLabelWidth">
          <el-input v-model="form.topic" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select v-model="form.status" placeholder="请选择状态" >
            <el-option label="删除" value="删除"></el-option>
            <el-option label="正常" value="正常"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,handleSubmitForm()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';

const api = new httpNew();
import {topics} from './data';

export default {
  name: 'topic',
  data() {
    return {
      topicId: this.$route.query.id,
      topicInfo: {},
      myInfo: {},
      tableData: [],
      dialogFormVisible: false,
      form: {
        topic: '',
        status: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.selectTopicList();
    // this.getMyInfo();
  },
  methods: {
    getMyInfo: function () {
      api.getCurrentUser().then(response => {
        var res = response.data;
        if (res.status == '0') {
          this.myInfo = res.data;
        } else {
          this.$message({message: res.msg, type: 'error'});
        }
      }).catch(err => {
        console.log(err);
      });
    },
    gotoTipsManage(row){
      var routeUrl=this.$router.resolve({name:'tipsManage',query: { topicId: row.id }});
      window.open(routeUrl.href, '_blank');
    },
    gotoTopicRecommend(row){
      var routeUrl=this.$router.resolve({name:'topicRecommendManage',query: { topicId: row.id }});
      window.open(routeUrl.href, '_blank');
    },
    selectTopic(row) {
      api.getTopicInfo(row.id).then(response => {
        var res = response.data;
        if (res.status == '0') {
          this.topicInfo = res.data;
        } else {
          this.$message({message: "查询失败", type: 'error'});
        }
      }).catch(err => {
        console.log(err);
      });
    },
    async selectTopicList() {
      try {
        let res = await api.getTopicList()
        if (res.data.status == '0') {
          let topicList = res.data.data;
          if (topicList && topicList.length > 0) {
            let arr = topicList.map(item => {
              return {
                id: item.id,
                topic: item.topic,
                status: item.status == '1' ? "正常" : "删除",
                cteTime: item.cteTime,
              }
            })
            this.tableData = arr;
          }
        } else {
          this.$message({message: res.msg, type: 'error'});
        }
      }catch (e) {}
      this.tableData = topics;
    },
    deleteTopic() {
      this.checkTopic(this.topicId);
      var data = {
        topicId: this.topicId,
      };
      api.deleteTopic(data).then(response => {
        var res = response.data;
      }).catch(err => {
        console.log(err);
      });
    },
    checkTopic(id) {
      api.checkTopicDelete(id).then(response => {
        var res = response.data;
        if (res.status == '0') {
          this.$message({message: res.msg, type: 'error'});
        }
      })
    },
    updateTopic(row) {
      this.form = row
      this.dialogFormVisible = true;
    },
    handleSubmitForm() {
      api.updateTopic(this.form).then(response => {
        var res = response.data;
        if (res.status == '0') {
          this.$message({message: res.msg, type: 'error'});
        }
      })
    }
  }
};
</script>
<style>
</style>
