<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="20">
          <el-col :span="20" :offset="2">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>敏感词管理</h2>
              </div>
              <div>
                <el-button @click="dialogFormVisible = true">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="20">
        <el-col :span="20" :offset="2">
          <el-table
            :data="wordlist"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="150">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容">
            </el-table-column>
            <!--<el-table-column
              prop="author"
              label="作者"
              width="120">
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="120">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="300">
            </el-table-column>-->
            <el-table-column
              fixed="right"
              label="操作"
              width="160">
              <template slot-scope="scope">
                <!--<el-button @click="selectSentiveWord(scope.row)" type="text" size="small">查看</el-button>-->
                <el-button @click='updateSentiveWord(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='getSentiveWordRule(scope.row)' type="text" size="small">屏蔽规则</el-button>
                <!--<el-button @click='deleteSentiveWord(scope.row)' type="text" size="small">删除</el-button>-->
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增敏感词" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
import {sentivewords} from './data';
import NavComp from './navComp.vue';

export default {
  name: "sentiveWord",
  components: {NavComp},
  data() {
    return {
      wordForm: {},
      wordlist: [],
      wordRule: {},
      dialogFormVisible: false,
      form: {
        content: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted(){
    // this.getSentiveWordByUserId()
    this.wordlist = sentivewords;
  },
  methods: {
    addWord() {
      this.checkSentiveWord(this.wordForm.word);
      this.wordlist.unshift(this.wordForm.word);
      this.wordForm = {};
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          api.saveSentiveWord(this.wordlist).then(response => {
            var res = response.data;
            if(res.status == "0"){
              this.$message({message: '保存成功' ,type: 'success'});
              this.$router.go(-1);
            }else {
              this.$message({'message': res.msg ,'type': 'error'});
            }
          }).catch(err => {
              console.log(err);
            });
        } else {
          return false;
        }
      });
    },
    checkSentiveWord(word) {
      api.checkSentiveWord(word).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.$message({message: '敏感词不允许提交' ,type: 'error'});
          return false;
        }else {
          return true;
        }
      })
    },
    getSentiveWordByUserId(){
      api.getSentiveWordByUserId(this.$store.state.user.userId).then(response => {
        var res = response.data;
        if (res.status == "0") {
          this.wordlist = res.data;
        } else {
          this.$message({'message': res.msg, 'type': 'error'});
        }
      })
    },
    updateSentiveWord(row) {
      api.updateSentiveWord(row).then(response => {
        var res = response.data;
        if (res.status == "0") {
          this.$message({message: '更新成功', type:'success'});
          this.getSentiveWordByUserId();
        } else {
          this.$message({message: res.msg, type: 'error'});
        }
      })
    },
    deleteSentiveWord(row) {
      this.$confirm('此操作将永久删除该敏感词, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.deleteSentiveWord(row.id).then(response => {
          var res = response.data;
          if (res.status == "0") {
            this.$message({message: '删除成功', type: 'success'});
            this.getSentiveWordByUserId();
          } else {
            this.$message({message: res.msg, type: 'error'});
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async getSentiveWordRule(row){
      let res = await api.getSentiveWordDetail(row.id)
      if (res.data.status == "0") {
        this.wordRule = res.data.data;
        this.$message({message: this.wordRule.content, type:'success'});
      } else {
        // this.$message({'message': res.msg, 'type': 'error'});
        this.$message({message: "作用于话题、小知识发表功能", type:'info'});
      }
    }
  },
};
</script>
<style>
</style>
