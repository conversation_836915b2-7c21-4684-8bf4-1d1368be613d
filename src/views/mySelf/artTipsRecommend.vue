<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>话题小知识推荐管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success" @click='handleOpenAddDialog'>新增</el-button>
        </el-row>
      </el-col>
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="80">
          </el-table-column>
          <el-table-column
            prop="tip"
            label="小知识"
            width="200">
          </el-table-column>
          <el-table-column
            prop="content"
            label="推荐内容"
            width="300">
          </el-table-column>
          <!--<el-table-column
            prop="authorName"
            label="作者"
            width="80">
          </el-table-column>-->
          <el-table-column
            prop="status"
            label="状态"
            width="60">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间"
            width="120">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作">
            <template slot-scope="scope">
              <el-button @click="detail(scope.row)" type="text" size="small">详情</el-button>
              <!--<el-button @click='handleAddArtTipRecommend(scope.row)' type="text" size="small">新增推荐</el-button>-->
              <el-button @click='handleRemoveArtTipRecommend(scope.row)' type="text" size="small">删除推荐</el-button>
              <el-button @click='handleUpdateArtTipRecommend(scope.row)' type="text" size="small">更新推荐</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="新增推荐" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="推荐小知识" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select v-model="form.tipId" placeholder="请选择小知识" >
            <el-option v-for='item in tips()' :key='item.id' :label="item.content" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐内容" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,handleAddArtTipRecommend()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑推荐" :visible.sync="editDialogFormVisible">
      <el-form :model="form">
        <el-form-item label="内容" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select v-model="form.status" placeholder="请选择状态" >
            <el-option label="删除" value="删除"></el-option>
            <el-option label="正常" value="正常"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="editDialogFormVisible = false,handleSubmitEditForm()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';
import {artTipsRecommends, topics, tips, getRandomNumber} from './data';

const api = new httpNew();

export default {
  name: 'artTipsRecommend',
  data() {
    return {
      tableData: [],
      artTipsDetail: {},
      dialogFormVisible: false,
      editDialogFormVisible: false,
      form: {
        id: '',
        content: '',
        topicId: '',
        tipId: '',
        status: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetTipsRecommend();
  },
  methods: {
    tips() {
      return tips
    },
    async handleGetTipsRecommend() {
      let res = await api.getArtTipRecommendList();
      if (res.data.status == 0){
        this.tableData = res.data.data;
      }
      this.tableData = artTipsRecommends;
      /*artTipsRecommends.forEach(item => {
        var randomNumber = getRandomNumber(0, Math.min(tips.length-1, topics.length-1));
        let tip = tips[randomNumber].content;
        let topic = topics[randomNumber].topic;
        let tipContent = '《' + topic + '》' + tip;
        let obj = {...item};
        obj.tip = tipContent;
        this.tableData.push(obj);
      })*/
    },
    handleOpenAddDialog(){
      // this.form.topicId = this.$route.query.topicId;
      // this.form.tipId = this.$route.query.tipId;
      this.dialogFormVisible = true;
    },
    detail(row) {
      api.getArtTipsDetail(row.id).then(res => {
        this.artTipsDetail = res.data.data;
      });
    },
    handleAddArtTipRecommend() {
      api.addArtTipRecommend(this.form).then(res => {
        if (res.data.status == "0") {
          this.$message.success('新增推荐成功');
          this.handleGetTipsRecommend()
        }else {
          this.$message.error('新增推荐失败');
        }
      });
    },
    handleRemoveArtTipRecommend(row) {
      api.removeArtTipRecommend(row.id).then(res => {
        if (res.data.status == "0") {
          this.$message.success('删除推荐成功');
          this.handleGetTipsRecommend()
        }else {
          this.$message.error('删除推荐失败');
        }
      });
    },
    handleUpdateArtTipRecommend(row) {
      this.form = {...row};
      this.editDialogFormVisible = true;
    },
    handleSubmitEditForm() {
      api.updateArtTipRecommend(this.form).then(res => {
        if (res.data.status == "0") {
          this.$message.success('更新推荐成功');
          this.handleGetTipsRecommend()
        }else {
          this.$message.error('更新推荐失败');
        }
      });
    }
  }
};
</script>
<style>
</style>
