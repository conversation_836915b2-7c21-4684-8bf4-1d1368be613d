<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>话题小知识收藏管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <el-col :span="10" :offset="2">
        <el-row>
          <el-button type="success" @click="dialogFormVisible = true">新增分类</el-button>
          <el-button type="success" @click="addCollectDialogFormVisible = true">新增收藏</el-button>
        </el-row>
      </el-col>
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="60">
          </el-table-column>
          <el-table-column
            prop="tip"
            label="小知识"
            width="200">
          </el-table-column>
          <el-table-column
            prop="content"
            label="收藏意见">
          </el-table-column>
          <!--<el-table-column
            prop="authorName"
            label="收藏人"
            width="120">
          </el-table-column>-->
          <el-table-column
            prop="typeName"
            label="类别"
            width="120">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间"
            width="120">
          </el-table-column>
          <!--<el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <el-button @click='handleAddArtTipCollect(scope.row)' type="text" size="small">收藏</el-button>
            </template>
          </el-table-column>-->
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="新增分类" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="名称" :label-width="formLabelWidth">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,handleSaveArtTipCollectType()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增收藏" :visible.sync="addCollectDialogFormVisible">
      <el-form :model="artTipsCollect">
        <el-form-item label="收藏小知识" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select v-model="artTipsCollect.tipId" placeholder="请选择小知识" >
            <el-option v-for='item in tips()' :key='item.id' :label="item.content" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收藏分类" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select v-model="artTipsCollect.type" placeholder="请选择分类" >
            <el-option v-for='item in collectTypes()' :key='item.id' :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addCollectDialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="addCollectDialogFormVisible = false,handleAddArtTipCollect()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';
import {artTipsCollects, collectTypes, tips} from './data';

const api = new httpNew();

export default {
  name: 'artTipsCollect',
  data() {
    return {
      tableData: [],
      artTipsDetail: {},
      artTipsCollect: {},
      dialogFormVisible: false,
      addCollectDialogFormVisible: false,
      form: {
        name: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetTipsCollectByTipId();
  },
  methods: {
    collectTypes() {
      return collectTypes
    },
    tips() {
      return tips
    },
    async handleGetTipsCollectByTipId() {
      let res = await api.getArtTipCollectList()
      this.tableData = res.data.status == 1 ? artTipsCollects : res.data.data;
      this.tableData.forEach(item => {
        // let obj = {...item};
        item.typeName = collectTypes.find(type => type.id == item.type).name;
        // this.tableData.push(obj);
      })
    },
    handleSaveArtTipCollectType(){
      api.saveArtTipCollectType().then(res => {
        if (res.data.status == "0"){
          this.$message.success("分类保存成功");
        }else {
          this.$message.error("分类保存失败")
        }
      })
    },
    handleAddArtTipCollect() {
      api.addArtTipCollect(this.artTipsCollect).then(res => {
        if (res.data.status == "0") {
          this.$message.success("收藏成功");
        }else {
          this.$message.error("收藏失败")
        }
      });
    }
  }
};
</script>
<style>
</style>
