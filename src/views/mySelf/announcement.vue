<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>系统公告管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success" @click="handleOpenAddDialog">新增</el-button>
        </el-row>
      </el-col>
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="150">
          </el-table-column>
          <el-table-column
            prop="content"
            label="内容">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <el-button @click="handleGetAnnouncement(scope.row.id)" type="text" size="small">查看</el-button>
              <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
              <el-button @click='handleDeleteAnnouncement(scope.row.id)' type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="新增公告" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="内容" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,handleAddAnnouncement()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑公告" :visible.sync="editDialogFormVisible">
      <el-form :model="form">
        <el-form-item label="内容" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="editDialogFormVisible = false,handleUpdateAnnouncement()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';
import {announcements} from './data';

const api = new httpNew();
export default {
  name: 'announcement',
  data() {
    return {
      tableData: [],
      announcementDetail: {},
      dialogFormVisible: false,
      editDialogFormVisible: false,
      form: {
        id: '',
        content: '',
      },
      formLabelWidth: '120px',
    };
  },
  created() {
    this.handleGetAnnouncementList();
  },
  methods: {
    handleGetAnnouncementList() {
      api.getAnnouncementList().then(res => {
        this.tableData = res.data;
      });
      this.tableData = announcements;
    },
    handleGetAnnouncement(id) {
      api.getAnnouncementDetail(id).then(res => {
        if (res.data.status == 0){
          this.announcementDetail = res.data.data;
        }else {
          this.$message.error("详情查询失败")
        }
      });
    },
    handleOpenAddDialog() {
      this.form = {
        id: '',
        content: '',
      };
      this.dialogFormVisible = true;
    },
    handleAddAnnouncement() {
      api.saveAnnouncement(this.form).then(res => {
        if (res.data.status == "0") {
          this.$message.success("新增成功");
          this.handleGetAnnouncementList();
        }else {
          this.$message.error("新增失败")
        }
      });
    },
    handleOpenEditDialog(row) {
      this.form = row;
      this.editDialogFormVisible = true;
    },
    handleUpdateAnnouncement() {
      api.updateAnnouncement(this.form).then(res => {
        if (res.data.status == "0") {
          this.$message.success("修改成功");
          this.handleGetAnnouncementList();
        }else {
          this.$message.error("修改失败")
        }
      });
    },
    handleDeleteAnnouncement(id) {
      api.deleteAnnouncement(id).then(res => {
        if (res.data.status == "0") {
          this.$message.success("删除成功");
          this.handleGetAnnouncementList();
        }else{
          this.$message.error("删除成功")
        }
      });
    },
  }
}
</script>

<style scoped>

</style>
